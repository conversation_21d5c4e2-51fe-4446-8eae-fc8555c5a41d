/* Licence
* Company: MCUSTUDIO
* Auther: Ahypnis.
* Version: V0.10
* Time: 2025/05/16
* Note:
*/
#ifndef __OLED_H__
#define __OLED_H__

#include <stdint.h>

#ifdef __cplusplus
extern "C" {
#endif

#define OLED_ADDR 0x78

#define OLED_WIDTH 128
#define OLED_HEIGHT 32
#define OLED_PAGES 4                    // OLED页数 (32/8 = 4)
#define OLED_BUFFER_SIZE 512            // 显示缓冲区大小 (128*32/8 = 512)

/* OLED传输状态枚举 */
typedef enum {
    OLED_TRANSFER_IDLE = 0,             // 空闲状态
    OLED_TRANSFER_BUSY,                 // 传输中
    OLED_TRANSFER_COMPLETE,             // 传输完成
    OLED_TRANSFER_ERROR                 // 传输错误
} oled_transfer_state_t;

/* OLED显示缓冲区结构体 */
typedef struct {
    uint8_t buffer[OLED_BUFFER_SIZE] __attribute__((aligned(4)));  // 显示缓冲区，4字节对齐优化DMA传输
    bool dirty_pages[OLED_PAGES];       // 脏页标记，标记哪些页需要刷新
    bool auto_refresh;                  // 自动刷新模式开关
    volatile oled_transfer_state_t transfer_state;  // DMA传输状态
    uint32_t refresh_count;             // 刷新计数器，用于性能监控
} oled_framebuffer_t;

/* 显示缓冲区管理函数 */
void OLED_FrameBuffer_Init(void);
void OLED_SetPixel(uint8_t x, uint8_t y, uint8_t color);
uint8_t OLED_GetPixel(uint8_t x, uint8_t y);
void OLED_ClearBuffer(void);
void OLED_MarkPageDirty(uint8_t page);
void OLED_ClearDirtyFlags(void);

/* 高级刷新API函数 */
void OLED_Refresh(void);                    // 全屏刷新
void OLED_RefreshPage(uint8_t page);        // 页刷新
void OLED_RefreshRegion(uint8_t x0, uint8_t y0, uint8_t x1, uint8_t y1);  // 区域刷新
void OLED_SetAutoRefresh(bool enable);      // 设置自动刷新模式

/* 批量传输API函数 */
void OLED_Write_Data_Bulk(uint8_t *data, uint16_t length);  // 批量数据传输
void OLED_Write_Cmd_Bulk(uint8_t *cmds, uint16_t length);   // 批量命令传输
bool OLED_DMA_Transfer_Complete(void);      // 检查DMA传输是否完成
void OLED_Wait_Transfer_Complete(void);     // 等待DMA传输完成

void OLED_Write_cmd(uint8_t cmd);
void OLED_Write_data(uint8_t data);
void OLED_ShowPic(uint8_t x0, uint8_t y0, uint8_t x1, uint8_t y1, uint8_t BMP[]);
void OLED_ShowHanzi(uint8_t x, uint8_t y, uint8_t no);
void OLED_ShowHzbig(uint8_t x, uint8_t y, uint8_t n);
void OLED_ShowFloat(uint8_t x, uint8_t y, float num, uint8_t accuracy, uint8_t fontsize);
void OLED_ShowNum(uint8_t x, uint8_t y, uint32_t num, uint8_t length, uint8_t fontsize);
void OLED_ShowStr(uint8_t x, uint8_t y, char *ch, uint8_t fontsize);
void OLED_ShowChar(uint8_t x, uint8_t y, uint8_t ch, uint8_t fontsize);
void OLED_Allfill(void);
void OLED_Set_Position(uint8_t x, uint8_t y);
void OLED_Clear(void);
void OLED_Display_On(void);
void OLED_Display_Off(void);
void OLED_Init(void);

#ifdef __cplusplus
  }
#endif

#endif  /*__OLED_H__*/
